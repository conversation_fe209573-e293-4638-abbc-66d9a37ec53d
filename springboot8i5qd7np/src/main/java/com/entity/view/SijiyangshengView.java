package com.entity.view;

import com.entity.SijiyangshengEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import org.apache.commons.beanutils.BeanUtils;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;

import java.io.Serializable;
import com.utils.EncryptUtil;
 

/**
 * 四季养生
 * 后端返回视图实体辅助类   
 * （通常后端关联的表或者自定义的字段需要返回使用）
 
 */
@TableName("sijiyangsheng")
public class SijiyangshengView  extends SijiyangshengEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public SijiyangshengView(){
	}
 
 	public SijiyangshengView(SijiyangshengEntity sijiyangshengEntity){
 	try {
			BeanUtils.copyProperties(this, sijiyangshengEntity);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
 		
	}


}
