package com.entity.view;

import com.entity.DiscusssijiyangshengEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import org.apache.commons.beanutils.BeanUtils;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;

import java.io.Serializable;
import com.utils.EncryptUtil;
 

/**
 * 四季养生评论表
 * 后端返回视图实体辅助类   
 * （通常后端关联的表或者自定义的字段需要返回使用）
 
 */
@TableName("discusssijiyangsheng")
public class DiscusssijiyangshengView  extends DiscusssijiyangshengEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public DiscusssijiyangshengView(){
	}
 
 	public DiscusssijiyangshengView(DiscusssijiyangshengEntity discusssijiyangshengEntity){
 	try {
			BeanUtils.copyProperties(this, discusssijiyangshengEntity);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
 		
	}


}
