package com.entity.view;

import com.entity.YangshengchanpinEntity;

import com.baomidou.mybatisplus.annotations.TableName;
import org.apache.commons.beanutils.BeanUtils;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;

import java.io.Serializable;
import com.utils.EncryptUtil;
 

/**
 * 养生产品
 * 后端返回视图实体辅助类   
 * （通常后端关联的表或者自定义的字段需要返回使用）
 
 */
@TableName("yangshengchanpin")
public class YangshengchanpinView  extends YangshengchanpinEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public YangshengchanpinView(){
	}
 
 	public YangshengchanpinView(YangshengchanpinEntity yangshengchanpinEntity){
 	try {
			BeanUtils.copyProperties(this, yangshengchanpinEntity);
		} catch (IllegalAccessException | InvocationTargetException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
 		
	}


}
