<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.SijifenleiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.SijifenleiEntity" id="sijifenleiMap">
        <result property="sijifenlei" column="sijifenlei"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.SijifenleiVO" >
		SELECT * FROM sijifenlei  sijifenlei         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.SijifenleiVO" >
		SELECT  sijifenlei.* FROM sijifenlei  sijifenlei 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.SijifenleiView" >

		SELECT  sijifenlei.* FROM sijifenlei  sijifenlei 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.SijifenleiView" >
		SELECT * FROM sijifenlei  sijifenlei <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
