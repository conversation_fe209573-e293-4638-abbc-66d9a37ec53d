<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ChanpinfenleiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.ChanpinfenleiEntity" id="chanpinfenleiMap">
        <result property="chanpinfenlei" column="chanpinfenlei"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.ChanpinfenleiVO" >
		SELECT * FROM chanpinfenlei  chanpinfenlei         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.ChanpinfenleiVO" >
		SELECT  chanpinfenlei.* FROM chanpinfenlei  chanpinfenlei 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.ChanpinfenleiView" >

		SELECT  chanpinfenlei.* FROM chanpinfenlei  chanpinfenlei 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.ChanpinfenleiView" >
		SELECT * FROM chanpinfenlei  chanpinfenlei <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
