<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.YangshengwenzhangDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.YangshengwenzhangEntity" id="yangshengwenzhangMap">
        <result property="wenzhangmingcheng" column="wenzhangmingcheng"/>
        <result property="wenzhangneirong" column="wenzhangneirong"/>
        <result property="fengmian" column="fengmian"/>
        <result property="fabushijian" column="fabushijian"/>
        <result property="thumbsupnum" column="thumbsupnum"/>
        <result property="crazilynum" column="crazilynum"/>
        <result property="clicktime" column="clicktime"/>
        <result property="clicknum" column="clicknum"/>
        <result property="discussnum" column="discussnum"/>
        <result property="storeupnum" column="storeupnum"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.YangshengwenzhangVO" >
		SELECT * FROM yangshengwenzhang  yangshengwenzhang         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.YangshengwenzhangVO" >
		SELECT  yangshengwenzhang.* FROM yangshengwenzhang  yangshengwenzhang 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.YangshengwenzhangView" >

		SELECT  yangshengwenzhang.* FROM yangshengwenzhang  yangshengwenzhang 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.YangshengwenzhangView" >
		SELECT * FROM yangshengwenzhang  yangshengwenzhang <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
