<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.YangshengzhishiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.YangshengzhishiEntity" id="yangshengzhishiMap">
        <result property="biaoti" column="biaoti"/>
        <result property="yangshengfenlei" column="yangshengfenlei"/>
        <result property="tupian" column="tupian"/>
        <result property="yufangjibing" column="yufangjibing"/>
        <result property="gongneng" column="gongneng"/>
        <result property="jinji" column="jinji"/>
        <result property="fabushijian" column="fabushijian"/>
        <result property="tuijianliyou" column="tuijianliyou"/>
        <result property="zhishixiangqing" column="zhishixiangqing"/>
        <result property="thumbsupnum" column="thumbsupnum"/>
        <result property="crazilynum" column="crazilynum"/>
        <result property="clicktime" column="clicktime"/>
        <result property="clicknum" column="clicknum"/>
        <result property="discussnum" column="discussnum"/>
        <result property="storeupnum" column="storeupnum"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.YangshengzhishiVO" >
		SELECT * FROM yangshengzhishi  yangshengzhishi         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.YangshengzhishiVO" >
		SELECT  yangshengzhishi.* FROM yangshengzhishi  yangshengzhishi 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.YangshengzhishiView" >

		SELECT  yangshengzhishi.* FROM yangshengzhishi  yangshengzhishi 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.YangshengzhishiView" >
		SELECT * FROM yangshengzhishi  yangshengzhishi <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

    <select id="selectValue" resultType="map" >
        SELECT ${params.xColumn}, ROUND(sum(${params.yColumn}),1) total FROM yangshengzhishi
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.xColumn}
    </select>

    <select id="selectTimeStatValue" resultType="map" >
        <if test = 'params.timeStatType == "日"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m-%d') ${params.xColumn}, sum(${params.yColumn}) total FROM yangshengzhishi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m-%d')
        </if>
        <if test = 'params.timeStatType == "月"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m') ${params.xColumn}, sum(${params.yColumn}) total FROM yangshengzhishi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m')
        </if>
        <if test = 'params.timeStatType == "年"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y') ${params.xColumn}, sum(${params.yColumn}) total FROM yangshengzhishi
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y')
        </if>
    </select>

    <select id="selectGroup" resultType="map" >
        SELECT ${params.column} , count(1) total FROM yangshengzhishi
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.column}
    </select>




</mapper>
