<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.YangshengchanpinDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.YangshengchanpinEntity" id="yangshengchanpinMap">
        <result property="chanpinmingcheng" column="chanpinmingcheng"/>
        <result property="chanpinfenlei" column="chanpinfenlei"/>
        <result property="chanpintupian" column="chanpintupian"/>
        <result property="chanpinpinpai" column="chanpinpinpai"/>
        <result property="chanpinguige" column="chanpinguige"/>
        <result property="chanpinchangjia" column="chanpinchangjia"/>
        <result property="shangjiariqi" column="shangjiariqi"/>
        <result property="chanpinjieshao" column="chanpinjieshao"/>
        <result property="onelimittimes" column="onelimittimes"/>
        <result property="alllimittimes" column="alllimittimes"/>
        <result property="clicktime" column="clicktime"/>
        <result property="clicknum" column="clicknum"/>
        <result property="discussnum" column="discussnum"/>
        <result property="price" column="price"/>
        <result property="storeupnum" column="storeupnum"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.YangshengchanpinVO" >
		SELECT * FROM yangshengchanpin  yangshengchanpin         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.YangshengchanpinVO" >
		SELECT  yangshengchanpin.* FROM yangshengchanpin  yangshengchanpin 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.YangshengchanpinView" >

		SELECT  yangshengchanpin.* FROM yangshengchanpin  yangshengchanpin 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.YangshengchanpinView" >
		SELECT * FROM yangshengchanpin  yangshengchanpin <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
