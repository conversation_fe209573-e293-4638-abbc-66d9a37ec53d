<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.SijiyangshengDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.SijiyangshengEntity" id="sijiyangshengMap">
        <result property="yangshengbiaoti" column="yangshengbiaoti"/>
        <result property="sijifenlei" column="sijifenlei"/>
        <result property="yangshengneirong" column="yangshengneirong"/>
        <result property="shipin" column="shipin"/>
        <result property="fabushijian" column="fabushijian"/>
        <result property="clicktime" column="clicktime"/>
        <result property="discussnum" column="discussnum"/>
        <result property="storeupnum" column="storeupnum"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.SijiyangshengVO" >
		SELECT * FROM sijiyangsheng  sijiyangsheng         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.SijiyangshengVO" >
		SELECT  sijiyangsheng.* FROM sijiyangsheng  sijiyangsheng 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.SijiyangshengView" >

		SELECT  sijiyangsheng.* FROM sijiyangsheng  sijiyangsheng 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.SijiyangshengView" >
		SELECT * FROM sijiyangsheng  sijiyangsheng <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
