<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.ChatDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.ChatEntity" id="chatMap">
        <result property="userid" column="userid"/>
        <result property="adminid" column="adminid"/>
        <result property="ask" column="ask"/>
        <result property="reply" column="reply"/>
        <result property="isreply" column="isreply"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.ChatVO" >
		SELECT * FROM chat  chat         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.ChatVO" >
		SELECT  chat.* FROM chat  chat 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.ChatView" >

		SELECT  chat.* FROM chat  chat 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.ChatView" >
		SELECT * FROM chat  chat <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
