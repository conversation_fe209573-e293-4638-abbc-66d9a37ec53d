<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.OrdersDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.OrdersEntity" id="ordersMap">
        <result property="orderid" column="orderid"/>
        <result property="tablename" column="tablename"/>
        <result property="userid" column="userid"/>
        <result property="goodid" column="goodid"/>
        <result property="goodname" column="goodname"/>
        <result property="picture" column="picture"/>
        <result property="buynumber" column="buynumber"/>
        <result property="price" column="price"/>
        <result property="total" column="total"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="address" column="address"/>
        <result property="tel" column="tel"/>
        <result property="consignee" column="consignee"/>
        <result property="logistics" column="logistics"/>
        <result property="remark" column="remark"/>
        <result property="goodtype" column="goodtype"/>
        <result property="sfsh" column="sfsh"/>
        <result property="shhf" column="shhf"/>
        <result property="role" column="role"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.OrdersVO" >
		SELECT * FROM orders  orders         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.OrdersVO" >
		SELECT  orders.* FROM orders  orders 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.OrdersView" >

		SELECT  orders.* FROM orders  orders 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.OrdersView" >
		SELECT * FROM orders  orders <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

    <select id="selectValue" resultType="map" >
        SELECT ${params.xColumn}, ROUND(sum(${params.yColumn}),1) total FROM orders
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.xColumn}
    </select>

    <select id="selectTimeStatValue" resultType="map" >
        <if test = 'params.timeStatType == "日"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m-%d') ${params.xColumn}, sum(${params.yColumn}) total FROM orders
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m-%d')
        </if>
        <if test = 'params.timeStatType == "月"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m') ${params.xColumn}, sum(${params.yColumn}) total FROM orders
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m')
        </if>
        <if test = 'params.timeStatType == "年"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y') ${params.xColumn}, sum(${params.yColumn}) total FROM orders
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y')
        </if>
    </select>

    <select id="selectGroup" resultType="map" >
        SELECT ${params.column} , count(1) total FROM orders
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.column}
    </select>




</mapper>
