<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.YinshileixingDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.YinshileixingEntity" id="yinshileixingMap">
        <result property="yinshileixing" column="yinshileixing"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.YinshileixingVO" >
		SELECT * FROM yinshileixing  yinshileixing         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.YinshileixingVO" >
		SELECT  yinshileixing.* FROM yinshileixing  yinshileixing 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.YinshileixingView" >

		SELECT  yinshileixing.* FROM yinshileixing  yinshileixing 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.YinshileixingView" >
		SELECT * FROM yinshileixing  yinshileixing <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
