<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.YangshengtuijianDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.YangshengtuijianEntity" id="yangshengtuijianMap">
        <result property="tuijianbiaoti" column="tuijianbiaoti"/>
        <result property="yinshileixing" column="yinshileixing"/>
        <result property="yundong" column="yundong"/>
        <result property="xueweianmo" column="xueweianmo"/>
        <result property="zhongyaodiaoli" column="zhongyaodiaoli"/>
        <result property="fengmian" column="fengmian"/>
        <result property="tuijianyuanyin" column="tuijianyuanyin"/>
        <result property="tuijianneirong" column="tuijianneirong"/>
        <result property="tuijianshijian" column="tuijianshijian"/>
        <result property="thumbsupnum" column="thumbsupnum"/>
        <result property="crazilynum" column="crazilynum"/>
        <result property="clicktime" column="clicktime"/>
        <result property="clicknum" column="clicknum"/>
        <result property="discussnum" column="discussnum"/>
        <result property="storeupnum" column="storeupnum"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.YangshengtuijianVO" >
		SELECT * FROM yangshengtuijian  yangshengtuijian         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.YangshengtuijianVO" >
		SELECT  yangshengtuijian.* FROM yangshengtuijian  yangshengtuijian 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.YangshengtuijianView" >

		SELECT  yangshengtuijian.* FROM yangshengtuijian  yangshengtuijian 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.YangshengtuijianView" >
		SELECT * FROM yangshengtuijian  yangshengtuijian <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	

    <select id="selectValue" resultType="map" >
        SELECT ${params.xColumn}, ROUND(sum(${params.yColumn}),1) total FROM yangshengtuijian
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.xColumn}
    </select>

    <select id="selectTimeStatValue" resultType="map" >
        <if test = 'params.timeStatType == "日"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m-%d') ${params.xColumn}, sum(${params.yColumn}) total FROM yangshengtuijian
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m-%d')
        </if>
        <if test = 'params.timeStatType == "月"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y-%m') ${params.xColumn}, sum(${params.yColumn}) total FROM yangshengtuijian
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y-%m')
        </if>
        <if test = 'params.timeStatType == "年"'>
                SELECT DATE_FORMAT(${params.xColumn},'%Y') ${params.xColumn}, sum(${params.yColumn}) total FROM yangshengtuijian
                <where> 1=1 ${ew.sqlSegment}</where>
                group by DATE_FORMAT(${params.xColumn},'%Y')
        </if>
    </select>

    <select id="selectGroup" resultType="map" >
        SELECT ${params.column} , count(1) total FROM yangshengtuijian
        <where> 1=1 ${ew.sqlSegment}</where>
        group by ${params.column}
    </select>




</mapper>
