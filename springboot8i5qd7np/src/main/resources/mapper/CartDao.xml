<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.CartDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.CartEntity" id="cartMap">
        <result property="tablename" column="tablename"/>
        <result property="userid" column="userid"/>
        <result property="goodid" column="goodid"/>
        <result property="goodname" column="goodname"/>
        <result property="picture" column="picture"/>
        <result property="buynumber" column="buynumber"/>
        <result property="price" column="price"/>
        <result property="goodtype" column="goodtype"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.CartVO" >
		SELECT * FROM cart  cart         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.CartVO" >
		SELECT  cart.* FROM cart  cart 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.CartView" >

		SELECT  cart.* FROM cart  cart 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.CartView" >
		SELECT * FROM cart  cart <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
