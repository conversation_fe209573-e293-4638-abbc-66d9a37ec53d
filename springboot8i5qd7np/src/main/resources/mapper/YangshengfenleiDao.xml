<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dao.YangshengfenleiDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.entity.YangshengfenleiEntity" id="yangshengfenleiMap">
        <result property="yangshengfenlei" column="yangshengfenlei"/>
    </resultMap>

	<select id="selectListVO"
		resultType="com.entity.vo.YangshengfenleiVO" >
		SELECT * FROM yangshengfenlei  yangshengfenlei         
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectVO"
		resultType="com.entity.vo.YangshengfenleiVO" >
		SELECT  yangshengfenlei.* FROM yangshengfenlei  yangshengfenlei 	
 		<where> 1=1 ${ew.sqlSegment}</where>
	</select>

    <select id="selectListView"
		resultType="com.entity.view.YangshengfenleiView" >

		SELECT  yangshengfenlei.* FROM yangshengfenlei  yangshengfenlei 	        
        <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	
	<select id="selectView"
		resultType="com.entity.view.YangshengfenleiView" >
		SELECT * FROM yangshengfenlei  yangshengfenlei <where> 1=1 ${ew.sqlSegment}</where>
	</select>
	


</mapper>
