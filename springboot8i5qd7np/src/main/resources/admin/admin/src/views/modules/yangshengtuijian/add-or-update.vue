<template>
	<div class="addEdit-block" :style='{"minHeight":"100vh","padding":"30px","fontSize":"14px","color":"#666","background":"#e9ecf3"}'>
		<el-form
			:style='{"border":"0px solid #bababa","padding":"40px 30px 20px","borderRadius":"0px","alignItems":"flex-start","flexWrap":"wrap","background":"rgba(255,255,255,1)","display":"flex","fontSize":"inherit"}'
			class="add-update-preview"
			ref="ruleForm"
			:model="ruleForm"
			:rules="rules"
			label-width="150px"
		>
			<template >
				<!-- 新闻标题 - 核心字段，突出显示 -->
				<el-form-item :style='{"border":"2px solid #1e3c4f","padding":"20px","margin":"0 0 40px 0","color":"inherit","borderRadius":"8px","width":"100%","fontSize":"inherit","background":"linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)","boxShadow":"0 4px 12px rgba(30,60,79,0.1)"}' class="input highlight-field" v-if="type!='info'"  label="新闻标题" prop="tuijianbiaoti">
					<div :style='{"fontSize":"16px","fontWeight":"bold","color":"#1e3c4f","marginBottom":"10px","display":"flex","alignItems":"center"}'>
						<i class="el-icon-edit" :style='{"marginRight":"8px","fontSize":"18px"}'></i>
						新闻标题 (核心字段)
					</div>
					<el-input v-model="ruleForm.tuijianbiaoti" placeholder="请输入新闻标题，这是新闻的核心标识" clearable :readonly="ro.tuijianbiaoti" :style='{"fontSize":"16px"}'></el-input>
					<div :style='{"fontSize":"12px","color":"#666","marginTop":"8px"}'>提示：新闻标题将在前台页面突出显示，请确保标题简洁明了且具有吸引力</div>
				</el-form-item>
				<el-form-item :style='{"border":"2px solid #1e3c4f","padding":"20px","margin":"0 0 40px 0","color":"inherit","borderRadius":"8px","width":"100%","fontSize":"inherit","background":"linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)","boxShadow":"0 4px 12px rgba(30,60,79,0.1)"}' v-else class="input highlight-field" label="新闻标题" prop="tuijianbiaoti">
					<div :style='{"fontSize":"16px","fontWeight":"bold","color":"#1e3c4f","marginBottom":"10px","display":"flex","alignItems":"center"}'>
						<i class="el-icon-view" :style='{"marginRight":"8px","fontSize":"18px"}'></i>
						新闻标题 (核心字段)
					</div>
					<el-input v-model="ruleForm.tuijianbiaoti" placeholder="新闻标题" readonly :style='{"fontSize":"16px"}'></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"1px solid #e9ecef","padding":"15px","margin":"0 0 25px 0","color":"inherit","borderRadius":"6px","width":"100%","fontSize":"inherit","background":"#fafbfc"}' class="select secondary-field" v-if="type!='info'"  label="饮食类型" prop="yinshileixing">
					<el-select :disabled="ro.yinshileixing" v-model="ruleForm.yinshileixing" placeholder="请选择饮食类型" style="width: 100%;">
						<el-option
							v-for="(item,index) in yinshileixingOptions"
							v-bind:key="index"
							:label="item"
							:value="item">
						</el-option>
					</el-select>
				</el-form-item>
				<el-form-item :style='{"border":"1px solid #e9ecef","padding":"15px","margin":"0 0 25px 0","color":"inherit","borderRadius":"6px","width":"100%","fontSize":"inherit","background":"#fafbfc"}' v-else class="input secondary-field" label="饮食类型" prop="yinshileixing">
					<el-input v-model="ruleForm.yinshileixing"
						placeholder="饮食类型" readonly></el-input>
				</el-form-item>
				<el-form-item :style='{"border":"1px solid #e9ecef","padding":"15px","margin":"0 0 25px 0","color":"inherit","borderRadius":"6px","width":"100%","fontSize":"inherit","background":"#fafbfc"}' class="upload secondary-field" v-if="type!='info' && !ro.fengmian" label="封面" prop="fengmian">
					<file-upload
						tip="点击上传封面"
						action="file/upload"
						:limit="3"
						:multiple="true"
						:fileUrls="ruleForm.fengmian?ruleForm.fengmian:''"
						@change="fengmianUploadChange"
					></file-upload>
				</el-form-item>
				<el-form-item :style='{"border":"1px solid #e9ecef","padding":"15px","margin":"0 0 25px 0","color":"inherit","borderRadius":"6px","width":"100%","fontSize":"inherit","background":"#fafbfc"}' class="upload secondary-field" v-else-if="ruleForm.fengmian" label="封面" prop="fengmian">
					<img v-if="ruleForm.fengmian.substring(0,4)=='http'" class="upload-img" style="margin-right:20px;" v-bind:key="index" :src="ruleForm.fengmian.split(',')[0]" width="100" height="100">
					<img v-else class="upload-img" style="margin-right:20px;" v-bind:key="index" v-for="(item,index) in ruleForm.fengmian.split(',')" :src="$base.url+item" width="100" height="100">
				</el-form-item>
				<el-form-item :style='{"border":"1px solid #e9ecef","padding":"15px","margin":"0 0 25px 0","color":"inherit","borderRadius":"6px","width":"100%","fontSize":"inherit","background":"#fafbfc"}' class="date secondary-field" v-if="type!='info'" label="发布时间" prop="tuijianshijian">
					<el-date-picker
						format="yyyy 年 MM 月 dd 日"
						value-format="yyyy-MM-dd"
						v-model="ruleForm.tuijianshijian"
						type="date"
						:readonly="ro.tuijianshijian"
						placeholder="发布时间"
						style="width: 100%;"
					></el-date-picker>
				</el-form-item>
				<el-form-item :style='{"border":"1px solid #e9ecef","padding":"15px","margin":"0 0 25px 0","color":"inherit","borderRadius":"6px","width":"100%","fontSize":"inherit","background":"#fafbfc"}' class="input secondary-field" v-else-if="ruleForm.tuijianshijian" label="发布时间" prop="tuijianshijian">
					<el-input v-model="ruleForm.tuijianshijian" placeholder="发布时间" readonly></el-input>
				</el-form-item>
			</template>
				<el-form-item :style='{"border":"0px solid #eee","padding":"0","margin":"0 0 36px 0","color":"inherit","borderRadius":"0px","width":"100%","fontSize":"inherit"}' class="textarea" v-if="type!='info'" label="运动" prop="yundong">
					<el-input
					  style="min-width: 200px; max-width: 600px;"
					  type="textarea"
					  :rows="8"
					  placeholder="运动"
					  v-model="ruleForm.yundong" >
					</el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","padding":"0","margin":"0 0 36px 0","color":"inherit","borderRadius":"0px","width":"100%","fontSize":"inherit"}' v-else-if="ruleForm.yundong" label="运动" prop="yundong">
					<span :style='{"fontSize":"14px","lineHeight":"40px","color":"inherit","fontWeight":"500","display":"inline-block"}'>{{ruleForm.yundong}}</span>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","padding":"0","margin":"0 0 36px 0","color":"inherit","borderRadius":"0px","width":"100%","fontSize":"inherit"}' class="textarea" v-if="type!='info'" label="穴位按摩" prop="xueweianmo">
					<el-input
					  style="min-width: 200px; max-width: 600px;"
					  type="textarea"
					  :rows="8"
					  placeholder="穴位按摩"
					  v-model="ruleForm.xueweianmo" >
					</el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","padding":"0","margin":"0 0 36px 0","color":"inherit","borderRadius":"0px","width":"100%","fontSize":"inherit"}' v-else-if="ruleForm.xueweianmo" label="穴位按摩" prop="xueweianmo">
					<span :style='{"fontSize":"14px","lineHeight":"40px","color":"inherit","fontWeight":"500","display":"inline-block"}'>{{ruleForm.xueweianmo}}</span>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","padding":"0","margin":"0 0 36px 0","color":"inherit","borderRadius":"0px","width":"100%","fontSize":"inherit"}' class="textarea" v-if="type!='info'" label="中药调理" prop="zhongyaodiaoli">
					<el-input
					  style="min-width: 200px; max-width: 600px;"
					  type="textarea"
					  :rows="8"
					  placeholder="中药调理"
					  v-model="ruleForm.zhongyaodiaoli" >
					</el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","padding":"0","margin":"0 0 36px 0","color":"inherit","borderRadius":"0px","width":"100%","fontSize":"inherit"}' v-else-if="ruleForm.zhongyaodiaoli" label="中药调理" prop="zhongyaodiaoli">
					<span :style='{"fontSize":"14px","lineHeight":"40px","color":"inherit","fontWeight":"500","display":"inline-block"}'>{{ruleForm.zhongyaodiaoli}}</span>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","padding":"0","margin":"0 0 36px 0","color":"inherit","borderRadius":"0px","width":"100%","fontSize":"inherit"}' class="textarea" v-if="type!='info'" label="推荐原因" prop="tuijianyuanyin">
					<el-input
					  style="min-width: 200px; max-width: 600px;"
					  type="textarea"
					  :rows="8"
					  placeholder="推荐原因"
					  v-model="ruleForm.tuijianyuanyin" >
					</el-input>
				</el-form-item>
				<el-form-item :style='{"border":"0px solid #eee","padding":"0","margin":"0 0 36px 0","color":"inherit","borderRadius":"0px","width":"100%","fontSize":"inherit"}' v-else-if="ruleForm.tuijianyuanyin" label="推荐原因" prop="tuijianyuanyin">
					<span :style='{"fontSize":"14px","lineHeight":"40px","color":"inherit","fontWeight":"500","display":"inline-block"}'>{{ruleForm.tuijianyuanyin}}</span>
				</el-form-item>
				<!-- 新闻内容 - 核心字段，突出显示 -->
				<el-form-item :style='{"border":"2px solid #1e3c4f","padding":"20px","margin":"0 0 40px 0","color":"inherit","borderRadius":"8px","width":"100%","fontSize":"inherit","background":"linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)","boxShadow":"0 4px 12px rgba(30,60,79,0.1)"}' class="editor-field highlight-field" v-if="type!='info'"  label="新闻内容" prop="tuijianneirong">
					<div :style='{"fontSize":"16px","fontWeight":"bold","color":"#1e3c4f","marginBottom":"15px","display":"flex","alignItems":"center"}'>
						<i class="el-icon-document" :style='{"marginRight":"8px","fontSize":"18px"}'></i>
						新闻内容 (核心字段)
					</div>
					<editor
						style="min-width: 100%; min-height: 400px; border: 2px solid #e9ecef; border-radius: 6px;"
						v-model="ruleForm.tuijianneirong"
						class="editor"
						action="file/upload">
					</editor>
					<div :style='{"fontSize":"12px","color":"#666","marginTop":"10px"}'>提示：新闻内容是文章的主体部分，支持富文本编辑，可插入图片、链接等多媒体内容</div>
				</el-form-item>
				<el-form-item :style='{"border":"2px solid #1e3c4f","padding":"20px","margin":"0 0 40px 0","color":"inherit","borderRadius":"8px","width":"100%","fontSize":"inherit","background":"linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)","boxShadow":"0 4px 12px rgba(30,60,79,0.1)"}' class="highlight-field" v-else-if="ruleForm.tuijianneirong" label="新闻内容" prop="tuijianneirong">
					<div :style='{"fontSize":"16px","fontWeight":"bold","color":"#1e3c4f","marginBottom":"15px","display":"flex","alignItems":"center"}'>
						<i class="el-icon-view" :style='{"marginRight":"8px","fontSize":"18px"}'></i>
						新闻内容 (核心字段)
					</div>
                    <div :style='{"fontSize":"15px","lineHeight":"1.8","color":"#333","fontWeight":"400","padding":"15px","background":"#fff","borderRadius":"6px","border":"1px solid #e9ecef","minHeight":"200px"}' v-html="ruleForm.tuijianneirong"></div>
                </el-form-item>
			<el-form-item :style='{"padding":"10px 0 20px","margin":"30px 0","alignItems":"center","textAlign":"center","display":"flex","width":"100%","perspective":"320px","-webkitPerspective":"320px","fontSize":"48px","justifyContent":"flex-start"}' class="btn">
				<el-button class="btn3"  v-if="type!='info'" type="success" @click="onSubmit">
					<span class="icon iconfont icon-tijiao16" :style='{"margin":"0 2px","fontSize":"18px","color":"inherit","display":"none"}'></span>
					保存修改
				</el-button>
				<el-button class="btn4" v-if="type!='info'" type="success" @click="back()">
					<span class="icon iconfont icon-quxiao09" :style='{"margin":"0 2px","fontSize":"18px","color":"inherit","display":"none"}'></span>
					取消修改
				</el-button>
				<el-button class="btn5" v-if="type=='info'" type="success" @click="back()">
					<span class="icon iconfont icon-fanhui01" :style='{"margin":"0 2px","fontSize":"18px","color":"inherit","display":"none"}'></span>
					返回
				</el-button>
			</el-form-item>
		</el-form>
    

  </div>
</template>
<script>
// 数字，邮件，手机，url，身份证校验
import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from "@/utils/validate";
export default {
	data() {
		let self = this
		var validateIdCard = (rule, value, callback) => {
			if(!value){
				callback();
			} else if (!checkIdCard(value)) {
				callback(new Error("请输入正确的身份证号码"));
			} else {
				callback();
			}
		};
		var validateUrl = (rule, value, callback) => {
			if(!value){
				callback();
			} else if (!isURL(value)) {
				callback(new Error("请输入正确的URL地址"));
			} else {
				callback();
			}
		};
		var validateMobile = (rule, value, callback) => {
			if(!value){
				callback();
			} else if (!isMobile(value)) {
				callback(new Error("请输入正确的手机号码"));
			} else {
				callback();
			}
		};
		var validatePhone = (rule, value, callback) => {
			if(!value){
				callback();
			} else if (!isPhone(value)) {
				callback(new Error("请输入正确的电话号码"));
			} else {
				callback();
			}
		};
		var validateEmail = (rule, value, callback) => {
			if(!value){
				callback();
			} else if (!isEmail(value)) {
				callback(new Error("请输入正确的邮箱地址"));
			} else {
				callback();
			}
		};
		var validateNumber = (rule, value, callback) => {
			if(!value){
				callback();
			} else if (!isNumber(value)) {
				callback(new Error("请输入数字"));
			} else {
				callback();
			}
		};
		var validateIntNumber = (rule, value, callback) => {
			if(!value){
				callback();
			} else if (!isIntNumer(value)) {
				callback(new Error("请输入整数"));
			} else {
				callback();
			}
		};
		return {
			id: '',
			type: '',
			
			
			ro:{
				tuijianbiaoti : false,
				yinshileixing : false,
				yundong : false,
				xueweianmo : false,
				zhongyaodiaoli : false,
				fengmian : false,
				tuijianyuanyin : false,
				tuijianneirong : false,
				tuijianshijian : false,
				thumbsupnum : false,
				crazilynum : false,
				clicktime : false,
				clicknum : false,
				discussnum : false,
				storeupnum : false,
			},
			
			
			ruleForm: {
				tuijianbiaoti: '',
				yinshileixing: '',
				yundong: '',
				xueweianmo: '',
				zhongyaodiaoli: '',
				fengmian: '',
				tuijianyuanyin: '',
				tuijianneirong: '',
				tuijianshijian: '',
				clicktime: '',
			},
		
			yinshileixingOptions: [],

			
			rules: {
				tuijianbiaoti: [
				],
				yinshileixing: [
				],
				yundong: [
				],
				xueweianmo: [
				],
				zhongyaodiaoli: [
				],
				fengmian: [
				],
				tuijianyuanyin: [
				],
				tuijianneirong: [
				],
				tuijianshijian: [
				],
				thumbsupnum: [
					{ validator: validateIntNumber, trigger: 'blur' },
				],
				crazilynum: [
					{ validator: validateIntNumber, trigger: 'blur' },
				],
				clicktime: [
				],
				clicknum: [
					{ validator: validateIntNumber, trigger: 'blur' },
				],
				discussnum: [
					{ validator: validateIntNumber, trigger: 'blur' },
				],
				storeupnum: [
					{ validator: validateIntNumber, trigger: 'blur' },
				],
			}
		};
	},
	props: ["parent"],
	computed: {



	},
    components: {
    },
	created() {
		this.ruleForm.tuijianshijian = this.getCurDate()
	},
	methods: {
		
		// 下载
		download(file){
			window.open(`${file}`)
		},
		// 初始化
		init(id,type) {
			if (id) {
				this.id = id;
				this.type = type;
			}
			if(this.type=='info'||this.type=='else'){
				this.info(id);
			}else if(this.type=='logistics'){
				this.logistics=false;
				this.info(id);
			}else if(this.type=='cross'){
				var obj = this.$storage.getObj('crossObj');
				for (var o in obj){
						if(o=='tuijianbiaoti'){
							this.ruleForm.tuijianbiaoti = obj[o];
							this.ro.tuijianbiaoti = true;
							continue;
						}
						if(o=='yinshileixing'){
							this.ruleForm.yinshileixing = obj[o];
							this.ro.yinshileixing = true;
							continue;
						}
						if(o=='yundong'){
							this.ruleForm.yundong = obj[o];
							this.ro.yundong = true;
							continue;
						}
						if(o=='xueweianmo'){
							this.ruleForm.xueweianmo = obj[o];
							this.ro.xueweianmo = true;
							continue;
						}
						if(o=='zhongyaodiaoli'){
							this.ruleForm.zhongyaodiaoli = obj[o];
							this.ro.zhongyaodiaoli = true;
							continue;
						}
						if(o=='fengmian'){
							this.ruleForm.fengmian = obj[o];
							this.ro.fengmian = true;
							continue;
						}
						if(o=='tuijianyuanyin'){
							this.ruleForm.tuijianyuanyin = obj[o];
							this.ro.tuijianyuanyin = true;
							continue;
						}
						if(o=='tuijianneirong'){
							this.ruleForm.tuijianneirong = obj[o];
							this.ro.tuijianneirong = true;
							continue;
						}
						if(o=='tuijianshijian'){
							this.ruleForm.tuijianshijian = obj[o];
							this.ro.tuijianshijian = true;
							continue;
						}
						if(o=='thumbsupnum'){
							this.ruleForm.thumbsupnum = obj[o];
							this.ro.thumbsupnum = true;
							continue;
						}
						if(o=='crazilynum'){
							this.ruleForm.crazilynum = obj[o];
							this.ro.crazilynum = true;
							continue;
						}
						if(o=='clicktime'){
							this.ruleForm.clicktime = obj[o];
							this.ro.clicktime = true;
							continue;
						}
						if(o=='clicknum'){
							this.ruleForm.clicknum = obj[o];
							this.ro.clicknum = true;
							continue;
						}
						if(o=='discussnum'){
							this.ruleForm.discussnum = obj[o];
							this.ro.discussnum = true;
							continue;
						}
						if(o=='storeupnum'){
							this.ruleForm.storeupnum = obj[o];
							this.ro.storeupnum = true;
							continue;
						}
				}
			}
			// 获取用户信息
			this.$http({
				url: `${this.$storage.get('sessionTable')}/session`,
				method: "get"
			}).then(({ data }) => {
				if (data && data.code === 0) {
					var json = data.data;
				} else {
					this.$message.error(data.msg);
				}
			});
            this.$http({
				url: `option/yinshileixing/yinshileixing`,
				method: "get"
            }).then(({ data }) => {
				if (data && data.code === 0) {
					this.yinshileixingOptions = data.data;
				} else {
					this.$message.error(data.msg);
				}
            });
			
		},
    // 多级联动参数

    info(id) {
      this.$http({
        url: `yangshengtuijian/info/${id}`,
        method: "get"
      }).then(({ data }) => {
        if (data && data.code === 0) {
        this.ruleForm = data.data;
        //解决前台上传图片后台不显示的问题
        let reg=new RegExp('../../../upload','g')//g代表全部
        this.ruleForm.tuijianneirong = this.ruleForm.tuijianneirong.replace(reg,'../../../springboot8i5qd7np/upload');
        } else {
          this.$message.error(data.msg);
        }
      });
    },


    // 提交
    onSubmit() {
	if(this.ruleForm.fengmian!=null) {
		this.ruleForm.fengmian = this.ruleForm.fengmian.replace(new RegExp(this.$base.url,"g"),"");
	}
var objcross = this.$storage.getObj('crossObj');
      //更新跨表属性
       var crossuserid;
       var crossrefid;
       var crossoptnum;
       if(this.type=='cross'){
                var statusColumnName = this.$storage.get('statusColumnName');
                var statusColumnValue = this.$storage.get('statusColumnValue');
                if(statusColumnName!='') {
                        var obj = this.$storage.getObj('crossObj');
                       if(statusColumnName && !statusColumnName.startsWith("[")) {
                               for (var o in obj){
                                 if(o==statusColumnName){
                                   obj[o] = statusColumnValue;
                                 }
                               }
                               var table = this.$storage.get('crossTable');
                             this.$http({
                                 url: `${table}/update`,
                                 method: "post",
                                 data: obj
                               }).then(({ data }) => {});
                       } else {
                               crossuserid=this.$storage.get('userid');
                               crossrefid=obj['id'];
                               crossoptnum=this.$storage.get('statusColumnName');
                               crossoptnum=crossoptnum.replace(/\[/,"").replace(/\]/,"");
                        }
                }
        }
		this.$refs["ruleForm"].validate(valid => {
			if (valid) {
				if(crossrefid && crossuserid) {
					this.ruleForm.crossuserid = crossuserid;
					this.ruleForm.crossrefid = crossrefid;
					let params = { 
						page: 1, 
						limit: 10, 
						crossuserid:this.ruleForm.crossuserid,
						crossrefid:this.ruleForm.crossrefid,
					} 
				this.$http({ 
					url: "yangshengtuijian/page", 
					method: "get", 
					params: params 
				}).then(({ 
					data 
				}) => { 
					if (data && data.code === 0) { 
						if(data.data.total>=crossoptnum) {
							this.$message.error(this.$storage.get('tips'));
							return false;
						} else {
							this.$http({
								url: `yangshengtuijian/${!this.ruleForm.id ? "save" : "update"}`,
								method: "post",
								data: this.ruleForm
							}).then(({ data }) => {
								if (data && data.code === 0) {
									this.$message({
										message: "操作成功",
										type: "success",
										duration: 1500,
										onClose: () => {
											this.parent.showFlag = true;
											this.parent.addOrUpdateFlag = false;
											this.parent.yangshengtuijianCrossAddOrUpdateFlag = false;
											this.parent.search();
											this.parent.contentStyleChange();
										}
									});
								} else {
									this.$message.error(data.msg);
								}
							});

						}
					} else { 
				} 
			});
		} else {
			this.$http({
				url: `yangshengtuijian/${!this.ruleForm.id ? "save" : "update"}`,
				method: "post",
			   data: this.ruleForm
			}).then(({ data }) => {
				if (data && data.code === 0) {
					this.$message({
						message: "操作成功",
						type: "success",
						duration: 1500,
						onClose: () => {
							this.parent.showFlag = true;
							this.parent.addOrUpdateFlag = false;
							this.parent.yangshengtuijianCrossAddOrUpdateFlag = false;
							this.parent.search();
							this.parent.contentStyleChange();
						}
					});
				} else {
					this.$message.error(data.msg);
			   }
			});
		 }
         }
       });
    },
    // 获取uuid
    getUUID () {
      return new Date().getTime();
    },
    // 返回
    back() {
      this.parent.showFlag = true;
      this.parent.addOrUpdateFlag = false;
      this.parent.yangshengtuijianCrossAddOrUpdateFlag = false;
      this.parent.contentStyleChange();
    },
    fengmianUploadChange(fileUrls) {
	    this.ruleForm.fengmian = fileUrls;
    },
  }
};
</script>
<style lang="scss" scoped>
	.amap-wrapper {
		width: 100%;
		height: 500px;
	}
	
	.search-box {
		position: absolute;
	}
	
	.el-date-editor.el-input {
		width: auto;
	}
	
	.add-update-preview .el-form-item /deep/ .el-form-item__label {
	  	  padding: 0 10px 0 0;
	  	  color: #999;
	  	  font-weight: 500;
	  	  display: inline-block;
	  	  width: 150px;
	  	  font-size: inherit;
	  	  line-height: 40px;
	  	  text-align: right;
	  	}
	
	.add-update-preview .el-form-item /deep/ .el-form-item__content {
	  margin-left: 150px;
	}
	
	.add-update-preview .el-input /deep/ .el-input__inner {
	  	  border-radius: 0px;
	  	  padding: 0 12px;
	  	  box-shadow: 0 0 0px rgba(64, 158, 255, .5);
	  	  color: inherit;
	  	  background: none;
	  	  width: 300px;
	  	  font-size: 14px;
	  	  border-color: #ccc;
	  	  border-width: 1px;
	  	  border-style: solid;
	  	  height: 39px;
	  	}
	.add-update-preview .el-input-number /deep/ .el-input__inner {
		text-align: left;
	  	  border-radius: 0px;
	  	  padding: 0 12px;
	  	  box-shadow: 0 0 0px rgba(64, 158, 255, .5);
	  	  color: inherit;
	  	  background: none;
	  	  width: 300px;
	  	  font-size: 14px;
	  	  border-color: #ccc;
	  	  border-width: 1px;
	  	  border-style: solid;
	  	  height: 39px;
	  	}
	.add-update-preview .el-input-number /deep/ .el-input-number__decrease {
		display: none;
	}
	.add-update-preview .el-input-number /deep/ .el-input-number__increase {
		display: none;
	}
	
	.add-update-preview .el-select /deep/ .el-input__inner {
	  	  border-radius: 0px;
	  	  padding: 0 10px;
	  	  box-shadow: 0 0 0px rgba(64, 158, 255, .5);
	  	  color: inherit;
	  	  background: none;
	  	  width: 300px;
	  	  font-size: 14px;
	  	  border-color: #ccc;
	  	  border-width: 1px;
	  	  border-style: solid;
	  	  height: 39px;
	  	}
	
	.add-update-preview .el-date-editor /deep/ .el-input__inner {
	  	  border-radius: 0px;
	  	  padding: 0 10px 0 30px;
	  	  box-shadow: 0 0 0px rgba(64, 158, 255, .5);
	  	  outline: none;
	  	  color: inherit;
	  	  background: none;
	  	  width: 300px;
	  	  font-size: 14px;
	  	  border-color: #ccc;
	  	  border-width: 1px;
	  	  border-style: solid;
	  	  height: 39px;
	  	}
	
	.add-update-preview /deep/ .el-upload--picture-card {
		background: transparent;
		border: 0;
		border-radius: 0;
		width: auto;
		height: auto;
		line-height: initial;
		vertical-align: middle;
	}
	
	.add-update-preview /deep/ .upload .upload-img {
	  	  cursor: pointer;
	  	  color: #bbb;
	  	  object-fit: cover;
	  	  font-size: 24px;
	  	  border-color: #ccc;
	  	  line-height: 80px;
	  	  border-radius: 0px;
	  	  background: none;
	  	  width: 120px;
	  	  border-width: 1px;
	  	  border-style: solid;
	  	  text-align: center;
	  	  height: 80px;
	  	}
	
	.add-update-preview /deep/ .el-upload-list .el-upload-list__item {
	  	  cursor: pointer;
	  	  color: #bbb;
	  	  object-fit: cover;
	  	  font-size: 24px;
	  	  border-color: #ccc;
	  	  line-height: 80px;
	  	  border-radius: 0px;
	  	  background: none;
	  	  width: 120px;
	  	  border-width: 1px;
	  	  border-style: solid;
	  	  text-align: center;
	  	  height: 80px;
	  	}
	
	.add-update-preview /deep/ .el-upload .el-icon-plus {
	  	  cursor: pointer;
	  	  color: #bbb;
	  	  object-fit: cover;
	  	  font-size: 24px;
	  	  border-color: #ccc;
	  	  line-height: 80px;
	  	  border-radius: 0px;
	  	  background: none;
	  	  width: 120px;
	  	  border-width: 1px;
	  	  border-style: solid;
	  	  text-align: center;
	  	  height: 80px;
	  	}
	
	.add-update-preview .el-textarea /deep/ .el-textarea__inner {
	  	  padding: 12px;
	  	  color: inherit;
	  	  font-size: 14px;
	  	  border-color: #ccc;
	  	  min-height: 120px;
	  	  border-radius: 0px;
	  	  box-shadow: 0 0 0px rgba(64, 158, 255, .5);
	  	  outline: none;
	  	  background: none;
	  	  width: auto;
	  	  border-width: 1px;
	  	  border-style: solid;
	  	  min-width: 400px;
	  	  height: auto;
	  	}
	
	.add-update-preview .btn .btn1 {
				border: 1px solid rgba(53, 184, 224, 0.1);
				cursor: pointer;
				padding: 0 20px;
				margin: 0px 4px;
				color: #fff;
				display: inline-block;
				font-size: 14px;
				line-height: 24px;
				border-radius: 0px;
				outline: none;
				background: #35b8e0;
				width: auto;
				height: 36px;
			}
	
	.add-update-preview .btn .btn1:hover {
			}
	
	.add-update-preview .btn .btn2 {
				border: 1px solid rgba(24, 138, 226, 0.1) ;
				cursor: pointer;
				border-radius: 0px;
				padding: 0 20px;
				margin: 0px 4px;
				outline: none;
				color: #fff;
				background: #188ae2;
				width: auto;
				font-size: 14px;
				line-height: 24px;
				height: 36px;
			}
	
	.add-update-preview .btn .btn2:hover {
			}
	
	.add-update-preview .btn .btn3 {
				border: 0px solid #0260ad;
				cursor: pointer;
				border-radius: 0px;
				padding: 0 20px;
				margin: 0px 4px;
				outline: none;
				color: #fff;
				background: #0e90d2;
				width: auto;
				font-size: 14px;
				line-height: 24px;
				height: 36px;
			}
	
	.add-update-preview .btn .btn3:hover {
			}
	
	.add-update-preview .btn .btn4 {
				border: 0px solid rgba(126, 96, 16, .2);
				cursor: pointer;
				border-radius: 0px;
				padding: 0 20px;
				margin: 0px 4px;
				outline: none;
				color: #fff;
				background: #727b84;
				width: auto;
				font-size: 14px;
				line-height: 24px;
				height: 36px;
			}
	
	.add-update-preview .btn .btn4:hover {
			}
	
	.add-update-preview .btn .btn5 {
				border: 1px solid rgba(114, 123, 132, 0.1);
				cursor: pointer;
				padding: 0 20px;
				margin: 4px;
				color: #fff;
				font-size: 14px;
				line-height: 24px;
				border-radius: 0px;
				outline: none;
				background: #727b84;
				width: auto;
				min-width: 90px;
				height: 36px;
			}
	
	.add-update-preview .btn .btn5:hover {
			}
</style>
