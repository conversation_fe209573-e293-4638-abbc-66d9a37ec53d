{"name": "front", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@lucky-canvas/vue": "^0.1.11", "animate.css": "^4.1.1", "axios": "^0.19.2", "core-js": "^3.6.5", "element-ui": "^2.15.5", "vue": "^2.6.11", "swiper": "^5.2.0", "vue-aplayer": "^1.6.1", "vue-baidu-map": "^0.21.22", "vue-amap": "^0.5.10", "vue-quill-editor": "^3.0.6", "vue-resource": "^1.5.3", "vue-router": "^3.5.2", "vue2-countdown": "^1.0.8", "vuex": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.13.1", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}