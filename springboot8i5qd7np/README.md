# 中医养生系统

## 项目概述

这是一个基于Spring Boot + Vue.js开发的中医养生系统，采用前后端分离架构，为用户提供全面的中医养生服务平台。系统包含养生知识分享、产品销售、体质测试、在线咨询等功能模块。

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 2.2.2
- **数据库**: MySQL 5.7+
- **ORM框架**: MyBatis Plus 2.3
- **权限控制**: Apache Shiro 1.3.2
- **数据验证**: Validation API 2.0.1
- **工具库**: Hutool 4.0.12, Apache Commons
- **文档处理**: Apache POI 3.11
- **AI接口**: 百度人工智能 Java SDK 4.4.1

### 前端技术栈
- **框架**: Vue.js 2.x
- **UI组件**: Element UI
- **构建工具**: Webpack
- **HTTP客户端**: Axios
- **路由管理**: Vue Router
- **状态管理**: Vuex

### 数据库设计
- **数据库名称**: `springboot8i5qd7np`
- **字符集**: UTF8MB4
- **存储引擎**: InnoDB
- **表数量**: 32个核心表

## 系统功能模块

### 🏥 用户管理模块
- **用户注册/登录**: 支持普通用户和管理员两种角色
- **个人信息管理**: 用户头像、基本信息维护
- **权限控制**: 基于角色的访问权限控制

### 🌿 养生内容模块
1. **养生新闻** (`yangshengtuijian`)
   - 新闻标题、饮食类型、运动建议
   - 穴位按摩、中药调理方案
   - 点赞、收藏、评论功能

2. **养生文章** (`yangshengwenzhang`)
   - 文章发布、编辑、管理
   - 富文本内容支持
   - 阅读统计、互动功能

3. **养生知识** (`yangshengzhishi`)
   - 按分类组织的养生知识
   - 预防疾病、功能介绍、禁忌说明
   - 推荐理由、详细描述

4. **养生分享** (`yangshengfenxiang`)
   - 用户原创内容分享
   - 适合人群标注
   - 审核机制、互动评论

5. **四季养生** (`sijiyangsheng`)
   - 按四季分类的养生内容
   - 视频支持、季节性建议

### 🛒 电商功能模块
1. **养生产品** (`yangshengchanpin`)
   - 产品展示、详情介绍
   - 品牌、规格、厂家信息
   - 价格管理、库存控制
   - 产品图片轮播

2. **购物车** (`cart`)
   - 商品添加、数量调整
   - 价格计算、批量操作

3. **订单管理** (`orders`)
   - 订单生成、状态跟踪
   - 支付流程、物流管理
   - 订单统计、数据分析

4. **地址管理** (`address`)
   - 收货地址维护
   - 默认地址设置

### 🔍 测试与咨询模块
1. **体质测试** (`tizhiceshi`)
   - 中医体质评估
   - 个性化养生建议

2. **在线咨询** (`chat`)
   - 用户提问、专家回复
   - 实时消息提醒

### 💬 社区功能模块
1. **论坛系统** (`forum`)
   - 帖子发布、回复
   - 话题分类、热门推荐

2. **评论系统** 
   - 针对各内容模块的评论功能
   - 统一的评论管理

3. **收藏功能** (`storeup`)
   - 内容收藏、个人收藏夹
   - 收藏统计

### ⚙️ 系统管理模块
1. **内容管理**
   - 轮播图管理 (`config`)
   - 公告信息 (`news`)
   - 关于我们 (`aboutus`)
   - 系统简介 (`systemintro`)

2. **分类管理**
   - 产品分类 (`chanpinfenlei`)
   - 养生分类 (`yangshengfenlei`) 
   - 四季分类 (`sijifenlei`)
   - 饮食类型 (`yinshileixing`)

## 项目结构

```
springboot8i5qd7np/
├── src/main/java/com/                 # 后端Java代码
│   ├── annotation/                    # 注解定义
│   ├── config/                       # 配置类
│   ├── controller/                   # 控制器层
│   ├── dao/                         # 数据访问层
│   ├── entity/                      # 实体类
│   ├── interceptor/                 # 拦截器
│   ├── service/                     # 业务逻辑层
│   └── utils/                       # 工具类
├── src/main/resources/
│   ├── admin/                       # 管理后台前端
│   ├── front/                       # 用户前台前端
│   ├── mapper/                      # MyBatis映射文件
│   ├── static/                      # 静态资源
│   └── application.yml              # 应用配置
├── db/                              # 数据库脚本
└── pom.xml                         # Maven配置
```

## 系统特色

### 🎯 业务特色
1. **专业性**: 专注中医养生领域，内容专业丰富
2. **个性化**: 提供体质测试和个性化推荐
3. **社区化**: 用户可分享经验，形成养生社区
4. **商业化**: 集成电商功能，实现内容变现
5. **季节性**: 按四季提供不同的养生建议

### 🔧 技术特色
1. **前后端分离**: 提高开发效率和系统扩展性
2. **权限控制**: 基于Shiro的细粒度权限管理
3. **响应式设计**: 支持多设备访问
4. **数据统计**: 丰富的数据统计和分析功能
5. **文件上传**: 支持图片、视频等多媒体内容

## 部署配置

### 环境要求
- JDK 1.8+
- MySQL 5.7+
- Node.js 12+
- Maven 3.6+

### 后端配置
1. **数据库配置** (`application.yml`)
   - 数据库地址: `**********************************************`
   - 用户名: `root`
   - 密码: `12345678`

2. **服务器配置**
   - 端口: `8080`
   - 上下文路径: `/springboot8i5qd7np`

### 前端配置
1. **管理后台**: `src/main/resources/admin/admin/`
2. **用户前台**: `src/main/resources/front/front/`
3. **API基址**: `http://localhost:8080/springboot8i5qd7np/`

### 部署步骤
1. 导入数据库脚本 `db/springboot8i5qd7np.sql`
2. 修改数据库连接配置
3. 构建后端项目: `mvn clean package`
4. 构建前端项目: 进入前端目录执行 `npm install && npm run build`
5. 启动应用: `java -jar target/springboot8i5qd7np-0.0.1-SNAPSHOT.jar`

## 默认账号

### 管理员账号
- 用户名: `admin`
- 密码: `admin`

### 测试用户账号
- 用户名: `用户账号1` ~ `用户账号8`
- 密码: `123456`

## 主要API接口

### 用户相关
- `POST /yonghu/login` - 用户登录
- `POST /yonghu/register` - 用户注册
- `GET /yonghu/session` - 获取用户信息

### 养生内容
- `GET /yangshengtuijian/list` - 获取养生新闻列表
- `GET /yangshengwenzhang/list` - 获取养生文章列表
- `GET /yangshengzhishi/list` - 获取养生知识列表

### 商品订单
- `POST /cart/save` - 添加购物车
- `POST /orders/save` - 创建订单
- `GET /orders/list` - 订单列表

## 数据库设计要点

### 核心表结构
1. **用户表** (`yonghu`): 存储用户基本信息、余额等
2. **养生内容表**: 支持富文本、图片、视频等多媒体内容
3. **商品表** (`yangshengchanpin`): 完整的电商商品信息
4. **订单表** (`orders`): 支持完整的订单流程管理
5. **评论表**: 统一的评论系统设计

### 特色设计
- **统计字段**: 各表都包含点击量、收藏数、评论数等统计字段
- **审核机制**: 用户生成内容支持审核流程
- **软删除**: 重要数据支持逻辑删除
- **时间戳**: 自动记录创建时间和更新时间

## 开发规范

### 后端规范
- 使用RESTful API设计
- 统一的返回结果格式
- 完善的异常处理机制
- 代码注释规范

### 前端规范
- 组件化开发
- 统一的样式规范
- 响应式布局设计
- 用户体验优化
