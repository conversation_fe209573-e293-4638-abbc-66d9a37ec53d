基于SpringBoot的中医养生系统,  已经修复上传视频无法预览播放bug

【角色与功能】
管理员（Admin）
养生分类管理、产品分类管理、饮食类型管理
养生推荐，新增养生推荐信息，填写标题、类型、封面、运动、穴位按摩、中药调理、推荐原因、推荐内容
养生文章，新增养生文章
养生知识，新增养生知识，填写标题、分类、图片、预防疾病、功能、推荐理由、知识详情
养生分享，查看编辑用户分享的养生分享信息
体质测试， 查看编辑用户新增的体质测试信息
四季养生，新增四季养生信息，填写标题、分类、上传视频、养生内容
养生产品，新增编辑养生产品，填写产品名称、分类、图片、品牌、规格、厂家、上架日期、单限、库存、价格、介绍
订单管理，查看订单详情，填写物流信息、发货，查看已完成订单，已发货订单
论坛管理，查看删除帖子详情，设置置顶，查看评论。
系统管理（关于我们、系统简介、轮播图管理、公告信息、公告信息分类、在线咨询）

用户（User）
查看养生推荐信息、养生文章、养生知识、养生分享、体质测试，观看四季养生视频、购买养生产品
添加养生分享信息、添加体质测试信息
查看养生产品信息、添加到购物车、购买、充值、支付、退款、查看物流信息、确认收货
查看论坛，发布，评论帖子。查看公告信息。在线咨询。

【技术架构】
SpringBoot、Mybatis-Plus、MySQL、Vue、ElementUI


【运行环境】
操作系统: Win10/11
开发工具:  ideaIU-2022.3.3.win
开发语言: Java (jdk-8u371)
项目管理工具: apache-maven-3.9.4
数据库: MySQL 8.0

运行步骤:
1、执行目录中的sql文件
2、解压项目压缩包
3、启动IDEA导入项目
4、等待Maven下载Jar包，下载完成之后点击启动项目
5、根据文档登录前台、后台首页

商品售出不退不换，商品可以运行，不可运行是自己软件问题，请自行配置，需要配置调试或修改功能私聊，不以运行不了理由退款，介意请绕道